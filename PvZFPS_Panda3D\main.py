#!/usr/bin/env python3
"""
PvZ FPS Panda3D - Main Entry Point
A Plants vs Zombies-inspired first-person shooter tower defense game

Author: AI Assistant
License: MIT
"""

import sys
import os
from direct.showbase.ShowBase import ShowBase
from panda3d.core import (
    WindowProperties, FrameBufferProperties, GraphicsPipe,
    AntialiasAttrib, RenderState, TransparencyAttrib,
    CollisionTraverser, CollisionHandlerQueue
)

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.constants import *
from src.game.game_state import GameState
from src.controllers.camera_controller import CameraController
from src.controllers.input_handler import InputHandler
from src.ui.hud import HUD


class PvZFPSGame(ShowBase):
    """
    Main game class that initializes Panda3D and manages the game loop
    """
    
    def __init__(self):
        """Initialize the game"""
        ShowBase.__init__(self)
        
        # Set up window properties
        self.setup_window()
        
        # Set up rendering
        self.setup_rendering()
        
        # Set up collision system
        self.setup_collision()
        
        # Initialize game systems
        self.setup_game_systems()
        
        # Start the game
        self.start_game()
    
    def setup_window(self):
        """Configure the game window"""
        props = WindowProperties()
        props.setTitle("PvZ FPS - Plants vs Zombies First Person Shooter")
        props.setSize(SCREEN_WIDTH, SCREEN_HEIGHT)
        props.setCursorHidden(True)  # Hide cursor for FPS mode
        self.win.requestProperties(props)
        
        # Set frame rate
        globalClock.setMode(globalClock.MLimited)
        globalClock.setFrameRate(TARGET_FPS)
    
    def setup_rendering(self):
        """Configure rendering settings"""
        # Enable antialiasing
        render.setAntialias(AntialiasAttrib.MAuto)
        
        # Enable transparency
        render.setTransparency(TransparencyAttrib.MAlpha)
        
        # Set background color (sky blue)
        self.setBackgroundColor(0.5, 0.8, 1.0, 1.0)
        
        # Disable default mouse camera control
        self.disableMouse()
        
        # Set up lighting
        self.setup_lighting()
    
    def setup_lighting(self):
        """Set up basic lighting"""
        from panda3d.core import AmbientLight, DirectionalLight, Vec4
        
        # Ambient light
        ambient_light = AmbientLight('ambient')
        ambient_light.setColor(Vec4(0.4, 0.4, 0.4, 1.0))
        ambient_light_np = render.attachNewNode(ambient_light)
        render.setLight(ambient_light_np)
        
        # Directional light (sun)
        directional_light = DirectionalLight('sun')
        directional_light.setColor(Vec4(0.8, 0.8, 0.6, 1.0))
        directional_light.setDirection(Vec3(-1, -1, -1))
        directional_light_np = render.attachNewNode(directional_light)
        render.setLight(directional_light_np)
    
    def setup_collision(self):
        """Set up collision detection system"""
        self.cTrav = CollisionTraverser()
        self.collision_queue = CollisionHandlerQueue()
        
        # Enable collision traverser
        base.cTrav = self.cTrav
    
    def setup_game_systems(self):
        """Initialize all game systems"""
        try:
            # Initialize input handler
            self.input_handler = InputHandler()
            
            # Initialize camera controller
            self.camera_controller = CameraController(self.camera, self.input_handler)
            
            # Initialize HUD
            self.hud = HUD()
            
            # Initialize game state
            self.game_state = GameState(self.input_handler, self.hud)
            
            print("Game systems initialized successfully")
            
        except Exception as e:
            print(f"Error initializing game systems: {e}")
            sys.exit(1)
    
    def start_game(self):
        """Start the main game loop"""
        print("Starting PvZ FPS Game...")
        print("Controls:")
        print("  WASD - Move")
        print("  Mouse - Look around")
        print("  1-5 - Select plants")
        print("  Left Click - Place plant")
        print("  ESC - Exit game")
        
        # Start the game state
        self.game_state.start()
        
        # Set up the main game task
        self.taskMgr.add(self.game_loop, "game_loop")
    
    def game_loop(self, task):
        """Main game loop task"""
        dt = globalClock.getDt()
        
        try:
            # Update camera controller
            self.camera_controller.update(dt)
            
            # Update game state
            self.game_state.update(dt)
            
            # Update HUD
            self.hud.update(dt)
            
        except Exception as e:
            print(f"Error in game loop: {e}")
            return task.done
        
        return task.cont
    
    def cleanup(self):
        """Clean up resources before exit"""
        print("Cleaning up game resources...")
        
        if hasattr(self, 'game_state'):
            self.game_state.cleanup()
        
        if hasattr(self, 'hud'):
            self.hud.cleanup()
        
        if hasattr(self, 'camera_controller'):
            self.camera_controller.cleanup()
        
        if hasattr(self, 'input_handler'):
            self.input_handler.cleanup()


def main():
    """Main entry point"""
    try:
        # Create and run the game
        game = PvZFPSGame()
        game.run()
        
    except KeyboardInterrupt:
        print("\nGame interrupted by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        if 'game' in locals():
            game.cleanup()
        print("Game ended")


if __name__ == "__main__":
    main()
