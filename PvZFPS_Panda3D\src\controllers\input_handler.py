"""
Input Handler for PvZ FPS Panda3D
Manages keyboard and mouse input for movement, camera control, and plant selection
"""

from direct.showbase.DirectObject import DirectObject
from panda3d.core import Vec3
from ..utils.constants import MOVEMENT_KEYS, PLANT_SELECTION_KEYS


class InputHandler(DirectObject):
    """
    Handles all input events including keyboard and mouse
    """
    
    def __init__(self):
        """Initialize input handler"""
        DirectObject.__init__(self)
        
        # Movement state
        self.movement_keys = {
            'forward': False,
            'backward': False,
            'left': False,
            'right': False
        }
        
        # Mouse state
        self.mouse_delta = Vec3(0, 0, 0)
        self.last_mouse_pos = None
        self.mouse_sensitivity = 0.5
        
        # Plant selection
        self.selected_plant = None
        self.plant_selection_callbacks = []
        
        # Mouse click callbacks
        self.mouse_click_callbacks = []
        
        # Set up input events
        self.setup_keyboard_events()
        self.setup_mouse_events()
        
        # Start mouse tracking task
        base.taskMgr.add(self.update_mouse, "update_mouse")
    
    def setup_keyboard_events(self):
        """Set up keyboard event handlers"""
        # Movement keys
        for direction, key in MOVEMENT_KEYS.items():
            self.accept(key, self.set_movement_key, [direction, True])
            self.accept(f"{key}-up", self.set_movement_key, [direction, False])
        
        # Plant selection keys
        for key, plant_type in PLANT_SELECTION_KEYS.items():
            self.accept(key, self.select_plant, [plant_type])
        
        # Escape key to exit
        self.accept("escape", self.exit_game)
    
    def setup_mouse_events(self):
        """Set up mouse event handlers"""
        # Mouse clicks
        self.accept("mouse1", self.on_mouse_click, ["left"])
        self.accept("mouse3", self.on_mouse_click, ["right"])
        
        # Disable default mouse camera control
        base.disableMouse()
    
    def set_movement_key(self, direction, pressed):
        """Set movement key state"""
        if direction in self.movement_keys:
            self.movement_keys[direction] = pressed
    
    def get_movement_vector(self):
        """Get normalized movement vector based on pressed keys"""
        movement = Vec3(0, 0, 0)
        
        if self.movement_keys['forward']:
            movement.y += 1
        if self.movement_keys['backward']:
            movement.y -= 1
        if self.movement_keys['left']:
            movement.x -= 1
        if self.movement_keys['right']:
            movement.x += 1
        
        # Normalize diagonal movement
        if movement.length() > 0:
            movement.normalize()
        
        return movement
    
    def update_mouse(self, task):
        """Update mouse delta for camera control"""
        if not base.mouseWatcherNode.hasMouse():
            return task.cont
        
        # Get current mouse position
        mouse_pos = base.mouseWatcherNode.getMouse()
        
        if self.last_mouse_pos is not None:
            # Calculate mouse delta
            delta_x = mouse_pos.x - self.last_mouse_pos.x
            delta_y = mouse_pos.y - self.last_mouse_pos.y
            
            self.mouse_delta = Vec3(delta_x, 0, delta_y) * self.mouse_sensitivity
        else:
            self.mouse_delta = Vec3(0, 0, 0)
        
        self.last_mouse_pos = mouse_pos
        
        # Reset mouse to center to prevent cursor from leaving window
        base.win.movePointer(0, base.win.getXSize() // 2, base.win.getYSize() // 2)
        
        return task.cont
    
    def get_mouse_delta(self):
        """Get mouse movement delta for this frame"""
        delta = self.mouse_delta
        self.mouse_delta = Vec3(0, 0, 0)  # Reset after reading
        return delta
    
    def select_plant(self, plant_type):
        """Select a plant type for placement"""
        self.selected_plant = plant_type
        print(f"Selected plant: {plant_type}")
        
        # Notify callbacks
        for callback in self.plant_selection_callbacks:
            callback(plant_type)
    
    def get_selected_plant(self):
        """Get currently selected plant type"""
        return self.selected_plant
    
    def on_mouse_click(self, button):
        """Handle mouse click events"""
        # Notify callbacks
        for callback in self.mouse_click_callbacks:
            callback(button)
    
    def add_plant_selection_callback(self, callback):
        """Add callback for plant selection events"""
        self.plant_selection_callbacks.append(callback)
    
    def remove_plant_selection_callback(self, callback):
        """Remove plant selection callback"""
        if callback in self.plant_selection_callbacks:
            self.plant_selection_callbacks.remove(callback)
    
    def add_mouse_click_callback(self, callback):
        """Add callback for mouse click events"""
        self.mouse_click_callbacks.append(callback)
    
    def remove_mouse_click_callback(self, callback):
        """Remove mouse click callback"""
        if callback in self.mouse_click_callbacks:
            self.mouse_click_callbacks.remove(callback)
    
    def exit_game(self):
        """Exit the game"""
        print("Exiting game...")
        base.userExit()
    
    def set_mouse_sensitivity(self, sensitivity):
        """Set mouse sensitivity"""
        self.mouse_sensitivity = sensitivity
    
    def cleanup(self):
        """Clean up input handler"""
        # Remove all event handlers
        self.ignoreAll()
        
        # Stop mouse tracking task
        base.taskMgr.remove("update_mouse")
        
        # Clear callbacks
        self.plant_selection_callbacks.clear()
        self.mouse_click_callbacks.clear()
