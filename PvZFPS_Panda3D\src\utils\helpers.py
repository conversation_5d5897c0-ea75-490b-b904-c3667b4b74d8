"""
Utility helper functions for PvZ FPS Panda3D
Contains math utilities, conversions, and common helper functions
"""

import math
from panda3d.core import Vec3, Point3, LVector3f
from .constants import LANE_POSITIONS, PLANT_GRID_START_X, PLANT_GRID_END_X, GRID_CELL_SIZE


def clamp(value, min_val, max_val):
    """Clamp a value between min and max bounds"""
    return max(min_val, min(value, max_val))


def lerp(a, b, t):
    """Linear interpolation between a and b by factor t (0-1)"""
    return a + (b - a) * t


def distance_2d(pos1, pos2):
    """Calculate 2D distance between two positions (ignoring Y axis)"""
    dx = pos1.x - pos2.x
    dz = pos1.z - pos2.z
    return math.sqrt(dx * dx + dz * dz)


def distance_3d(pos1, pos2):
    """Calculate 3D distance between two positions"""
    return (pos1 - pos2).length()


def normalize_angle(angle):
    """Normalize angle to be between -180 and 180 degrees"""
    while angle > 180:
        angle -= 360
    while angle < -180:
        angle += 360
    return angle


def world_to_grid(world_pos):
    """Convert world position to grid coordinates"""
    # Find closest lane
    lane_index = get_lane_from_position(world_pos.z)
    
    # Find grid column
    grid_x = (world_pos.x - PLANT_GRID_START_X) / GRID_CELL_SIZE
    grid_col = int(round(grid_x))
    
    return lane_index, grid_col


def grid_to_world(lane_index, grid_col):
    """Convert grid coordinates to world position"""
    if lane_index < 0 or lane_index >= len(LANE_POSITIONS):
        return None
    
    world_x = PLANT_GRID_START_X + (grid_col * GRID_CELL_SIZE)
    world_z = LANE_POSITIONS[lane_index]
    world_y = 0.0
    
    return Point3(world_x, world_y, world_z)


def get_lane_from_position(z_pos):
    """Get the lane index from a Z world position"""
    closest_lane = 0
    min_distance = abs(z_pos - LANE_POSITIONS[0])
    
    for i, lane_z in enumerate(LANE_POSITIONS):
        distance = abs(z_pos - lane_z)
        if distance < min_distance:
            min_distance = distance
            closest_lane = i
    
    return closest_lane


def is_valid_plant_position(lane_index, grid_col):
    """Check if a grid position is valid for plant placement"""
    if lane_index < 0 or lane_index >= len(LANE_POSITIONS):
        return False
    
    max_cols = int((PLANT_GRID_END_X - PLANT_GRID_START_X) / GRID_CELL_SIZE)
    if grid_col < 0 or grid_col >= max_cols:
        return False
    
    return True


def get_direction_vector(from_pos, to_pos):
    """Get normalized direction vector from one position to another"""
    direction = to_pos - from_pos
    direction.normalize()
    return direction


def rotate_vector_2d(vector, angle_degrees):
    """Rotate a 2D vector by the given angle in degrees"""
    angle_rad = math.radians(angle_degrees)
    cos_a = math.cos(angle_rad)
    sin_a = math.sin(angle_rad)
    
    new_x = vector.x * cos_a - vector.z * sin_a
    new_z = vector.x * sin_a + vector.z * cos_a
    
    return Vec3(new_x, vector.y, new_z)


def point_in_bounds(point, min_bounds, max_bounds):
    """Check if a point is within the given bounds"""
    return (min_bounds.x <= point.x <= max_bounds.x and
            min_bounds.y <= point.y <= max_bounds.y and
            min_bounds.z <= point.z <= max_bounds.z)


def create_billboard_from_texture(texture, scale=1.0):
    """Create a billboard card from a texture for 2D sprites in 3D space"""
    from panda3d.core import CardMaker, TransparencyAttrib
    
    cm = CardMaker("billboard")
    cm.setFrame(-scale, scale, -scale, scale)
    card = render.attachNewNode(cm.generate())
    card.setTexture(texture)
    card.setTransparency(TransparencyAttrib.MAlpha)
    card.setBillboardPointEye()
    
    return card


def load_texture_safe(texture_path):
    """Safely load a texture with error handling"""
    from panda3d.core import Texture
    
    try:
        texture = loader.loadTexture(texture_path)
        if texture is None:
            print(f"Warning: Could not load texture: {texture_path}")
            # Return a default white texture
            texture = Texture()
            texture.setup2dTexture(1, 1, Texture.TUnsignedByte, Texture.FRgba)
            texture.setRamImageAs([255, 255, 255, 255], "RGBA")
        return texture
    except Exception as e:
        print(f"Error loading texture {texture_path}: {e}")
        # Return a default white texture
        texture = Texture()
        texture.setup2dTexture(1, 1, Texture.TUnsignedByte, Texture.FRgba)
        texture.setRamImageAs([255, 255, 255, 255], "RGBA")
        return texture


def format_time(seconds):
    """Format seconds into MM:SS format"""
    minutes = int(seconds // 60)
    seconds = int(seconds % 60)
    return f"{minutes:02d}:{seconds:02d}"


def ease_in_out(t):
    """Smooth easing function for animations"""
    return t * t * (3.0 - 2.0 * t)


def random_in_range(min_val, max_val):
    """Generate random float between min and max values"""
    import random
    return random.uniform(min_val, max_val)


def random_choice(choices):
    """Randomly select from a list of choices"""
    import random
    return random.choice(choices)
