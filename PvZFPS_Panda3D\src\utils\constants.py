"""
Game constants for PvZ FPS Panda3D
Contains all game configuration values, lane positions, costs, speeds, etc.
"""

# Screen and rendering settings
SCREEN_WIDTH = 1024
SCREEN_HEIGHT = 768
TARGET_FPS = 60

# Camera settings
CAMERA_SPEED = 10.0
MOUSE_SENSITIVITY = 0.5
CAMERA_BOUNDS = {
    'min_x': -15.0,
    'max_x': 15.0,
    'min_z': -10.0,
    'max_z': 10.0,
    'min_y': 0.5,
    'max_y': 5.0
}

# Lane system
NUM_LANES = 5
LANE_WIDTH = 4.0
LANE_SPACING = 6.0
LANE_LENGTH = 40.0
GRID_CELL_SIZE = 2.0

# Lane positions (Y coordinates)
LANE_POSITIONS = [
    -12.0,  # Lane 1 (top)
    -6.0,   # Lane 2
    0.0,    # Lane 3 (center)
    6.0,    # Lane 4
    12.0    # Lane 5 (bottom)
]

# Plant placement grid
PLANT_GRID_COLS = 9  # Number of plant placement columns per lane
PLANT_GRID_START_X = -18.0  # Starting X position for plant grid
PLANT_GRID_END_X = 18.0     # Ending X position for plant grid

# Zombie spawn and movement
ZOMBIE_SPAWN_X = 25.0
ZOMBIE_TARGET_X = -25.0
ZOMBIE_BASE_SPEED = 2.0

# Sun economy
STARTING_SUN = 50
SUN_ORB_VALUE = 25
SUN_FALL_INTERVAL = 10.0  # seconds
SUNFLOWER_PRODUCTION_INTERVAL = 24.0  # seconds
SUNFLOWER_SUN_VALUE = 25

# Plant costs and stats
PLANT_COSTS = {
    'sunflower': 50,
    'peashooter': 100,
    'wallnut': 50,
    'cherry_bomb': 150,
    'snow_pea': 175
}

PLANT_HEALTH = {
    'sunflower': 100,
    'peashooter': 100,
    'wallnut': 300,
    'cherry_bomb': 100,
    'snow_pea': 100
}

PLANT_DAMAGE = {
    'peashooter': 20,
    'cherry_bomb': 200,
    'snow_pea': 20
}

PLANT_FIRE_RATE = {
    'peashooter': 1.5,  # seconds between shots
    'snow_pea': 1.5
}

# Zombie types and stats
ZOMBIE_HEALTH = {
    'basic': 100,
    'cone': 200,
    'fast': 60
}

ZOMBIE_SPEED = {
    'basic': 1.0,
    'cone': 0.8,
    'fast': 1.5
}

ZOMBIE_DAMAGE = {
    'basic': 10,
    'cone': 10,
    'fast': 8
}

# Projectile settings
PROJECTILE_SPEED = 15.0
PROJECTILE_LIFETIME = 5.0  # seconds

# Wave management
WAVE_SPAWN_INTERVAL = 30.0  # seconds between waves
ZOMBIES_PER_WAVE_BASE = 5
WAVE_DIFFICULTY_MULTIPLIER = 1.2

# Audio settings
MASTER_VOLUME = 0.7
SFX_VOLUME = 0.8
MUSIC_VOLUME = 0.6

# Asset paths
ASSETS_DIR = "assets"
SPRITES_DIR = f"{ASSETS_DIR}/sprites"
SOUNDS_DIR = f"{ASSETS_DIR}/sounds"
PLANTS_DIR = f"{SPRITES_DIR}/plants"
ZOMBIES_DIR = f"{SPRITES_DIR}/zombies"
UI_DIR = f"{SPRITES_DIR}/ui"

# Colors (RGBA)
COLORS = {
    'white': (1, 1, 1, 1),
    'black': (0, 0, 0, 1),
    'red': (1, 0, 0, 1),
    'green': (0, 1, 0, 1),
    'blue': (0, 0, 1, 1),
    'yellow': (1, 1, 0, 1),
    'orange': (1, 0.5, 0, 1),
    'purple': (0.5, 0, 1, 1),
    'lane_marker': (0.2, 0.8, 0.2, 0.5),
    'grid_marker': (0.5, 0.5, 0.5, 0.3),
    'ui_background': (0.1, 0.1, 0.1, 0.8)
}

# Input keys
MOVEMENT_KEYS = {
    'forward': 'w',
    'backward': 's',
    'left': 'a',
    'right': 'd'
}

PLANT_SELECTION_KEYS = {
    '1': 'sunflower',
    '2': 'peashooter',
    '3': 'wallnut',
    '4': 'cherry_bomb',
    '5': 'snow_pea'
}

# Debug settings
DEBUG_MODE = True
SHOW_COLLISION_BOXES = False
SHOW_GRID_LINES = True
SHOW_FPS = True
