"""
Camera Controller for PvZ FPS Panda3D
Manages first-person camera movement with mouse look and bounded movement area
"""

import math
from panda3d.core import Vec3, Point3
from ..utils.constants import CAMERA_SPEED, CAMERA_BOUNDS
from ..utils.helpers import clamp


class CameraController:
    """
    First-person camera controller with mouse look and movement bounds
    """
    
    def __init__(self, camera, input_handler):
        """Initialize camera controller"""
        self.camera = camera
        self.input_handler = input_handler
        
        # Camera position and rotation
        self.position = Point3(0, 2, 0)  # Start in center of defensive area
        self.pitch = 0.0  # Up/down rotation
        self.yaw = 0.0    # Left/right rotation
        
        # Movement settings
        self.speed = CAMERA_SPEED
        self.bounds = CAMERA_BOUNDS
        
        # Set initial camera position
        self.update_camera_transform()
    
    def update(self, dt):
        """Update camera based on input"""
        # Handle mouse look
        self.handle_mouse_look(dt)
        
        # Handle movement
        self.handle_movement(dt)
        
        # Update camera transform
        self.update_camera_transform()
    
    def handle_mouse_look(self, dt):
        """Handle mouse look for camera rotation"""
        mouse_delta = self.input_handler.get_mouse_delta()
        
        if mouse_delta.length() > 0:
            # Update yaw (left/right)
            self.yaw -= mouse_delta.x * 100.0  # Scale for reasonable sensitivity
            
            # Update pitch (up/down)
            self.pitch += mouse_delta.z * 100.0  # Note: Z is used for vertical mouse movement
            
            # Clamp pitch to prevent over-rotation
            self.pitch = clamp(self.pitch, -89.0, 89.0)
            
            # Normalize yaw
            while self.yaw > 180.0:
                self.yaw -= 360.0
            while self.yaw < -180.0:
                self.yaw += 360.0
    
    def handle_movement(self, dt):
        """Handle WASD movement with bounds checking"""
        movement_vector = self.input_handler.get_movement_vector()
        
        if movement_vector.length() > 0:
            # Calculate movement in world space based on camera orientation
            forward = self.get_forward_vector()
            right = self.get_right_vector()
            
            # Calculate movement direction
            move_direction = Vec3(0, 0, 0)
            move_direction += forward * movement_vector.y  # Forward/backward
            move_direction += right * movement_vector.x    # Left/right
            
            # Apply movement speed and delta time
            movement = move_direction * self.speed * dt
            
            # Calculate new position
            new_position = self.position + movement
            
            # Apply bounds checking
            new_position = self.apply_bounds(new_position)
            
            # Update position
            self.position = new_position
    
    def get_forward_vector(self):
        """Get camera forward vector based on yaw (ignoring pitch for movement)"""
        yaw_rad = math.radians(self.yaw)
        forward = Vec3(
            math.sin(yaw_rad),
            math.cos(yaw_rad),
            0  # Keep movement horizontal
        )
        forward.normalize()
        return forward
    
    def get_right_vector(self):
        """Get camera right vector based on yaw"""
        yaw_rad = math.radians(self.yaw + 90)  # Right is 90 degrees from forward
        right = Vec3(
            math.sin(yaw_rad),
            math.cos(yaw_rad),
            0  # Keep movement horizontal
        )
        right.normalize()
        return right
    
    def get_look_direction(self):
        """Get camera look direction including pitch"""
        yaw_rad = math.radians(self.yaw)
        pitch_rad = math.radians(self.pitch)
        
        direction = Vec3(
            math.sin(yaw_rad) * math.cos(pitch_rad),
            math.cos(yaw_rad) * math.cos(pitch_rad),
            math.sin(pitch_rad)
        )
        direction.normalize()
        return direction
    
    def apply_bounds(self, position):
        """Apply movement bounds to keep camera in defensive area"""
        bounded_pos = Point3(
            clamp(position.x, self.bounds['min_x'], self.bounds['max_x']),
            clamp(position.y, self.bounds['min_y'], self.bounds['max_y']),
            clamp(position.z, self.bounds['min_z'], self.bounds['max_z'])
        )
        return bounded_pos
    
    def update_camera_transform(self):
        """Update camera position and rotation"""
        # Set camera position
        self.camera.setPos(self.position)
        
        # Set camera rotation (HPR: Heading, Pitch, Roll)
        self.camera.setHpr(self.yaw, self.pitch, 0)
    
    def get_position(self):
        """Get current camera position"""
        return self.position
    
    def get_rotation(self):
        """Get current camera rotation (yaw, pitch)"""
        return (self.yaw, self.pitch)
    
    def set_position(self, position):
        """Set camera position"""
        self.position = self.apply_bounds(position)
        self.update_camera_transform()
    
    def set_rotation(self, yaw, pitch):
        """Set camera rotation"""
        self.yaw = yaw
        self.pitch = clamp(pitch, -89.0, 89.0)
        self.update_camera_transform()
    
    def set_speed(self, speed):
        """Set movement speed"""
        self.speed = speed
    
    def get_camera_ray(self):
        """Get ray from camera position in look direction (for mouse picking)"""
        origin = self.position
        direction = self.get_look_direction()
        return origin, direction
    
    def cleanup(self):
        """Clean up camera controller"""
        # Reset camera to default position
        self.camera.setPos(0, -10, 0)
        self.camera.setHpr(0, 0, 0)
